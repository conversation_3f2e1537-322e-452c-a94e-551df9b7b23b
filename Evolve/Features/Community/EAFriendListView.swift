import SwiftUI
import SwiftData

// 🔑 根本修复：使用统一导航架构

/// 好友列表界面 - 星际伙伴系统
/// 遵循iOS设计规范，展示用户的好友关系和互动状态
struct EAFriendListView: View {

    // MARK: - ViewModel

    /// 🔑 第一阶段重构：引入ViewModel，完成MVVM架构的第一步
    @StateObject private var viewModel = EAFriendListViewModel()

    // MARK: - 环境依赖

    @EnvironmentObject var sessionManager: EASessionManager
    @Environment(\.repositoryContainer) private var repositoryContainer
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态管理

    // ✅ 第二阶段重构：从环境中消费服务，而非自行创建
    @EnvironmentObject var friendshipService: EAFriendshipService
    @EnvironmentObject var notificationService: EAFriendNotificationService
    
    // 🔑 第二阶段重构：状态变量已迁移到ViewModel，这里保留必要的本地状态
    // 注意：大部分状态已迁移到viewModel，只保留View层特有的状态

    // 🔑 根本修复：ActionSheet状态管理
    @State private var selectedFriendForActionSheet: FriendDisplayItem? = nil

    // 🔑 批次三优化：简化状态管理，使用枚举统一管理确认对话框
    @State private var confirmationAction: ConfirmationAction? = nil

    /// 确认操作类型枚举
    private enum ConfirmationAction: Identifiable {
        case block(FriendDisplayItem)
        case unblock(FriendDisplayItem)

        var id: String {
            switch self {
            case .block(let friend): return "block_\(friend.friendshipId)"
            case .unblock(let friend): return "unblock_\(friend.friendshipId)"
            }
        }
    }
    
    // MARK: - 初始化
    // ✅ 第二阶段重构：移除自定义初始化，改为从环境中消费服务
    
    // MARK: - 主视图
    
    var body: some View {
        // 🔑 最终修复：移除嵌套NavigationStack，使用父级EACommunityView的NavigationStack
        GeometryReader { geometry in
                let isIPad = UIDevice.current.userInterfaceIdiom == .pad
                
                ZStack {
                    // 🌟 星域数字宇宙背景
                    stellarUniverseBackground

                    VStack(spacing: 0) {
                    // 搜索栏
                    searchBar

                    // 分段控制器
                    segmentedControl

                    // 内容区域
                    contentArea
                        .padding(.horizontal, isIPad ? max(40, (geometry.size.width - 600) / 2) : 0) // iPad居中显示，限制最大宽度
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .navigationTitle("星际伙伴")
            // 🔑 紧急修复：移除重复的navigationDestination声明，避免导航冲突
            // 所有导航目标由父级EACommunityView统一处理
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack(spacing: 16) {
                        // 🔑 批次三新增：屏蔽管理入口
                        NavigationLink(value: EACommunityNavigationDestination.blockedUserList) {
                            Image(systemName: "eye.slash")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(.red.opacity(0.8))
                        }

                        // 添加好友按钮
                        NavigationLink(destination:
                            EAAddFriendView()
                                .environmentObject(sessionManager)
                                .environmentObject(friendshipService)  // ✅ 注入好友服务
                                .environment(\.repositoryContainer, repositoryContainer)
                        ) {
                            Image(systemName: "person.badge.plus")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(Color.hexColor("40E0D0"))
                        }
                    }
                }
            }
            .onAppear {
                // 🔑 第一阶段重构：设置ViewModel依赖项（安全解包）
                if let container = repositoryContainer {
                    viewModel.setDependencies(
                        sessionManager: sessionManager,
                        repositoryContainer: container,
                        friendshipService: friendshipService,
                        notificationService: notificationService
                    )
                }

                // 🔑 第二阶段重构：使用ViewModel的数据加载方法
                Task {
                    await viewModel.initializeData()
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("blockingStatusChanged"))) { notification in
                // 🔑 关键修复：监听屏蔽状态变化，确保UI实时同步
                Task {
                    await viewModel.refreshData()
                }
            }
            // 🔑 修复：使用friendshipId而不是对象
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("DeleteFriendRequested"))) { notification in
                if let friendshipId = notification.object as? UUID {
                    viewModel.deleteFriend(friendshipId: friendshipId)
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("BlockFriendRequested"))) { notification in
                if let friendshipId = notification.object as? UUID {
                    viewModel.blockFriend(friendshipId: friendshipId)
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("FriendRequestAccepted"))) { _ in
                // 🔑 好友请求被接受后重新加载数据
                Task { @MainActor in
                    await viewModel.refreshData()
                }
            }
            // 🔑 最终修复：移除冗余的navigationDestination，使用父级EACommunityView的统一导航处理
            .alert("提示", isPresented: $viewModel.showAlert) {
                Button("确定") { }
            } message: {
                Text(viewModel.alertMessage)
            }
            // 🔑 根本修复：移除本地navigationDestination，使用父级统一处理
        }
    }
    
    // MARK: - 子视图组件
    
    /// 星域数字宇宙背景
    private var stellarUniverseBackground: some View {
        ZStack {
            // 深空背景 - 优化为更有层次感的宇宙色调
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.hexColor("0B1426"), // 深蓝夜空
                    Color.hexColor("1A2332"), // 中层蓝灰
                    Color.hexColor("2D3748"), // 浅层灰蓝
                    Color.hexColor("1A365D")  // 深青蓝
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            // 星云效果层
            RadialGradient(
                gradient: Gradient(colors: [
                    Color.hexColor("40E0D0").opacity(0.08),
                    Color.clear
                ]),
                center: .topTrailing,
                startRadius: 50,
                endRadius: 300
            )
            .ignoresSafeArea()
            
            // 星点效果 - 增加青色调星点
            ForEach(0..<40, id: \.self) { index in
                Circle()
                    .fill(
                        index % 3 == 0 
                        ? Color.hexColor("40E0D0").opacity(Double.random(in: 0.3...0.7))
                        : Color.white.opacity(Double.random(in: 0.2...0.5))
                    )
                    .frame(width: CGFloat.random(in: 1...3))
                    .position(
                        x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                        y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                    )
            }
        }
    }
    
    /// 搜索栏
    private var searchBar: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.6))
            
            TextField("搜索星际伙伴...", text: $viewModel.searchText)
                .textFieldStyle(PlainTextFieldStyle())
                .font(.system(size: 16))
                .foregroundColor(.white)
                .accentColor(Color.hexColor("40E0D0"))
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.hexColor("40E0D0").opacity(0.3), lineWidth: 1)
                )
        )
        .padding(.horizontal, 16)
        .padding(.top, 8)
    }
    
    /// 分段控制器
    private var segmentedControl: some View {
        HStack(spacing: 0) {
            // 好友列表选项卡
            segmentButton(
                title: "好友列表",
                index: 0,
                badge: nil
            )
            
            // 好友请求选项卡
            segmentButton(
                title: "好友请求",
                index: 1,
                badge: friendshipService.pendingRequests.count > 0 ? friendshipService.pendingRequests.count : nil
            )
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.1))
        )
        .padding(.horizontal, 16)
        .padding(.top, 16)
    }
    
    /// 分段按钮
    private func segmentButton(title: String, index: Int, badge: Int?) -> some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.2)) {
                viewModel.selectedTab = index
            }

            // 🔑 MVVM架构修复：通过ViewModel统一管理业务逻辑
            if index == 1 {
                #if DEBUG
                // 调试环境下记录用户点击好友请求标签，开始加载数据
                #endif
                Task {
                    await viewModel.loadPendingRequests()
                }
            }
        }) {
            HStack(spacing: 6) {
                Text(title)
                    .font(.system(size: 16, weight: viewModel.selectedTab == index ? .semibold : .medium))
                    .foregroundColor(viewModel.selectedTab == index ? .white : .white.opacity(0.6))
                
                // 徽章
                if let badgeCount = badge {
                    Text("\(badgeCount)")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            Capsule()
                                .fill(Color.red)
                        )
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(viewModel.selectedTab == index ? Color.hexColor("40E0D0").opacity(0.3) : Color.clear)
            )
        }
    }
    
    /// 内容区域
    private var contentArea: some View {
        VStack(spacing: 0) {
            if viewModel.selectedTab == 0 {
                friendsListContent
            } else {
                friendRequestsContent
            }
        }
    }
    
    /// 好友列表内容
    private var friendsListContent: some View {
        Group {
            if viewModel.isLoading {
                loadingView
            } else if viewModel.filteredFriendDisplayData.isEmpty {
                emptyFriendsView
            } else {
                friendsList
            }
        }
    }
    
    /// 🔑 外科手术修复：使用List确保NavigationLink最佳表现
    private var friendsList: some View {
        List(viewModel.filteredFriendDisplayData, id: \.friendshipId) { friendItem in
            // 🔑 根本修复：清晰的交互分离架构
            HStack(spacing: 0) {
                // 主要导航区域 - 占据大部分空间
                NavigationLink(value: EACommunityNavigationDestination.friendChat(friendItem.friendshipId)) {
                    // 🔑 系统性修复：直接使用显示项的屏蔽状态（实现瞬间响应）
                    FriendRowView(
                        friendshipData: friendItem.displayData,
                        isBlocked: friendItem.isBlocked
                    )
                    .contentShape(Rectangle()) // 确保整个区域可点击
                }
                .buttonStyle(PlainButtonStyle()) // 移除默认按钮样式

                // 更多操作按钮 - 独立的交互区域
                Button(action: {
                    selectedFriendForActionSheet = friendItem
                }) {
                    Image(systemName: "ellipsis.circle")
                        .font(.system(size: 20))
                        .foregroundColor(.white.opacity(0.6))
                        .frame(width: 44, height: 44) // 提供足够的点击区域
                }
                .buttonStyle(BorderlessButtonStyle())
            }
            .listRowBackground(Color.clear)
            .listRowSeparator(.hidden)
            .listRowInsets(EdgeInsets(top: 6, leading: 16, bottom: 6, trailing: 16))
            // 🔑 关键修复：添加ID以确保列表项状态更新
            .id("\(friendItem.friendshipId)_\(friendItem.isBlocked)")
        }
        .listStyle(.plain) // 使用.plain样式确保NavigationLink表现正常
        .scrollContentBackground(.hidden) // 隐藏List默认背景
        .refreshable {
            // 🔑 下拉刷新
            await viewModel.refreshData()
        }
        // 🔑 修复：使用现代API替换已弃用的ActionSheet
        .confirmationDialog("好友操作", isPresented: .constant(selectedFriendForActionSheet != nil), titleVisibility: .visible) {
            if let friendItem = selectedFriendForActionSheet {
                // 🔑 批次三修改：动态生成按钮，支持屏蔽/取消屏蔽切换
                let isBlocked = friendItem.userId.map { viewModel.isUserBlocked(userId: $0) } ?? false

                Button("删除好友", role: .destructive) {
                    viewModel.deleteFriend(friendshipId: friendItem.friendshipId)
                    selectedFriendForActionSheet = nil
                }

                // 🔑 批次三核心功能：动态屏蔽/取消屏蔽按钮
                if isBlocked {
                    Button("取消屏蔽") {
                        confirmationAction = .unblock(friendItem)
                        selectedFriendForActionSheet = nil
                    }
                } else {
                    Button("屏蔽用户", role: .destructive) {
                        confirmationAction = .block(friendItem)
                        selectedFriendForActionSheet = nil
                    }
                }

                Button("取消", role: .cancel) {
                    selectedFriendForActionSheet = nil
                }
            }
        }
        // 🔑 批次三优化：统一的确认对话框
        .alert(item: $confirmationAction) { action in
            switch action {
            case .block(let friend):
                return Alert(
                    title: Text("确认屏蔽"),
                    message: Text("屏蔽后您将无法收到该用户的消息，且该用户也无法向您发送消息。确定要屏蔽吗？"),
                    primaryButton: .destructive(Text("确认屏蔽")) {
                        viewModel.blockFriend(friendshipId: friend.friendshipId)
                    },
                    secondaryButton: .cancel(Text("取消"))
                )
            case .unblock(let friend):
                return Alert(
                    title: Text("确认取消屏蔽"),
                    message: Text("取消屏蔽后，您和该用户将可以正常发送消息。确定要取消屏蔽吗？"),
                    primaryButton: .default(Text("确认取消")) {
                        if let userId = friend.userId {
                            viewModel.unblockUser(userId: userId)
                        }
                    },
                    secondaryButton: .cancel(Text("取消"))
                )
            }
        }
    }
    
    /// 好友请求内容
    private var friendRequestsContent: some View {
        Group {
            if friendshipService.isLoading {
                loadingView
            } else if friendshipService.pendingRequests.isEmpty {
                emptyRequestsView
            } else {
                requestsList
            }
        }
    }
    
    /// 请求列表
    private var requestsList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(friendshipService.pendingRequests, id: \.id) { request in
                    FriendRequestRowView(
                        request: request,
                        friendshipService: friendshipService
                    )
                }
            }
            .padding(.horizontal, 16)
            .padding(.top, 16)
        }
        .refreshable {
            await friendshipService.loadPendingRequests()
        }
    }
    
    /// 加载视图
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: Color.hexColor("40E0D0")))
                .scaleEffect(1.2)
            
            Text("正在加载星际伙伴...")
                .font(.system(size: 16))
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.clear)
    }
    
    /// 空好友列表视图
    private var emptyFriendsView: some View {
        VStack(spacing: 24) {
            // 宇宙探索图标
            Image(systemName: "person.2.circle")
                .font(.system(size: 64))
                .foregroundColor(Color.hexColor("40E0D0").opacity(0.6))
            
            VStack(spacing: 12) {
                Text("还没有星际伙伴")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)
                
                Text("添加好友，开始您的宇宙探索之旅")
                    .font(.system(size: 16))
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
            }
            
            NavigationLink(destination:
                EAAddFriendView()
                    .environmentObject(sessionManager)
                    .environmentObject(friendshipService)
                    .environment(\.repositoryContainer, repositoryContainer)
            ) {
                HStack(spacing: 8) {
                    Image(systemName: "person.badge.plus")
                        .font(.system(size: 16, weight: .medium))
                    Text("添加好友")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.hexColor("40E0D0"))
                )
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.clear)
    }
    
    /// 空请求列表视图
    private var emptyRequestsView: some View {
        VStack(spacing: 24) {
            Image(systemName: "tray.circle")
                .font(.system(size: 64))
                .foregroundColor(Color.hexColor("40E0D0").opacity(0.6))
            
            VStack(spacing: 12) {
                Text("暂无好友请求")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)
                
                Text("当有人想成为您的星际伙伴时，请求会显示在这里")
                    .font(.system(size: 16))
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.clear)
    }
    
    // MARK: - 第二阶段重构：计算属性已迁移到ViewModel
    
    // MARK: - 第二阶段重构：数据加载和操作方法已迁移到ViewModel
}

// MARK: - 辅助数据结构

/// 🔑 系统性修复：好友显示项目，支持实时屏蔽状态更新
/// 遵循ID使用规范：friendshipId用于关系管理，userId用于屏蔽功能
struct FriendDisplayItem: Identifiable {
    let id: UUID
    let friendshipId: UUID
    let userId: UUID?  // 🔑 用户ID，用于屏蔽状态检查（符合ID使用规范）
    let displayData: FriendshipDisplayData
    var isBlocked: Bool = false  // 🔑 系统性修复：添加屏蔽状态属性，支持实时UI更新

    init(friendshipId: UUID, userId: UUID? = nil, displayData: FriendshipDisplayData, isBlocked: Bool = false) {
        self.id = friendshipId // 使用friendshipId作为id
        self.friendshipId = friendshipId
        self.userId = userId
        self.displayData = displayData
        self.isBlocked = isBlocked
    }
}

// MARK: - 预览

#Preview {
    NavigationView {
        let container = EARepositoryContainerImpl(modelContainer: try! EAAppSchema.createPreviewContainer())
        let sessionManager = EASessionManager()

        let integrityGuard = EAUserIntegrityGuard(repositoryContainer: container)  // 🔑 修复：创建完整性守护服务

        EAFriendListView()
            .environmentObject(sessionManager)
            .environmentObject(EAFriendshipService(repositoryContainer: container, sessionManager: sessionManager, integrityGuard: integrityGuard))
            .environmentObject(EAFriendNotificationService(repositoryContainer: container))
            .environment(\.repositoryContainer, container)
    }
}
