import SwiftUI
import SwiftData

/// EAFriendChatView 使用示例
/// 展示MVVM重构后的聊天界面如何使用
struct EAFriendChatViewExample: View {
    
    // MARK: - 示例数据
    
    @State private var sampleFriendshipId = UUID()
    @StateObject private var sessionManager = EASessionManager()
    @State private var repositoryContainer: EARepositoryContainer?
    @State private var chatService: EAFriendChatService?
    
    var body: some View {
        NavigationView {
            VStack {
                if let repositoryContainer = repositoryContainer,
                   let chatService = chatService {
                    // 🔑 MVVM重构后的聊天界面
                    EAFriendChatView(friendshipId: sampleFriendshipId)
                        .environmentObject(sessionManager)
                        .environmentObject(chatService)
                        .environment(\.repositoryContainer, repositoryContainer)
                } else {
                    // 加载中状态
                    VStack(spacing: 20) {
                        ProgressView()
                            .scaleEffect(1.5)
                        
                        Text("正在初始化聊天服务...")
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(EABackgroundView(style: .auraSpace, showParticles: true))
                }
            }
        }
        .task {
            await initializeServices()
        }
    }
    
    // MARK: - 初始化方法
    
    /// 初始化服务和依赖
    private func initializeServices() async {
        do {
            // 创建ModelContainer
            let container = try EAAppSchema.createPreviewContainer()
            let repoContainer = EARepositoryContainerImpl(modelContainer: container)
            
            // 创建AI数据桥接
            let aiDataBridge = EACommunityAIDataBridge(repositoryContainer: repoContainer)
            
            // 创建聊天服务
            let service = EAFriendChatService(
                repositoryContainer: repoContainer,
                sessionManager: sessionManager,
                aiDataBridge: aiDataBridge
            )
            
            // 更新状态
            await MainActor.run {
                self.repositoryContainer = repoContainer
                self.chatService = service
            }
            
            // 创建示例数据
            try await createSampleData(in: repoContainer)
            
        } catch {
            print("❌ 初始化服务失败: \(error.localizedDescription)")
        }
    }
    
    /// 创建示例数据
    private func createSampleData(in container: EARepositoryContainer) async throws {
        do {
            // 创建示例用户
            let currentUser = EAUser(username: "当前用户", email: "<EMAIL>")
            let friendUser = EAUser(username: "好友用户", email: "<EMAIL>")
            
            // 保存用户
            try await container.userRepository.saveUser(currentUser)
            try await container.userRepository.saveUser(friendUser)
            
            // 确保用户有社交档案
            let currentProfile = try await container.userRepository.ensureSocialProfile(for: currentUser)
            let friendProfile = try await container.userRepository.ensureSocialProfile(for: friendUser)
            
            // 创建好友关系 - 使用Repository的标准方法
            let friendship = try await container.friendshipRepository.createFriendship(
                user1ProfileId: currentProfile.id,
                user2ProfileId: friendProfile.id
            )
            
            // 更新示例好友关系ID
            await MainActor.run {
                self.sampleFriendshipId = friendship.id
            }
            
            // 设置当前用户会话
            try await sessionManager.setCurrentUser(currentUser)
            
            print("✅ 示例数据创建成功")
            
        } catch {
            print("❌ 创建示例数据失败: \(error.localizedDescription)")
        }
    }
}

// MARK: - SwiftUI预览

#Preview("聊天界面示例") {
    EAFriendChatViewExample()
        .preferredColorScheme(.dark)
}

// MARK: - 使用说明

/*
 ## EAFriendChatView MVVM重构使用指南
 
 ### 1. 基本使用
 ```swift
 EAFriendChatView(friendshipId: friendshipId)
     .environmentObject(sessionManager)
     .environmentObject(chatService)
     .environment(\.repositoryContainer, repositoryContainer)
 ```
 
 ### 2. 必需的环境对象
 - `EASessionManager`: 用户会话管理
 - `EAFriendChatService`: 聊天服务
 - `EARepositoryContainer`: 数据仓库容器
 
 ### 3. ViewModel特性
 - 完全的状态管理：所有UI状态都由ViewModel管理
 - 响应式更新：使用@Published属性自动更新UI
 - 错误处理：统一的错误状态管理
 - 异步操作：支持异步数据加载和消息发送
 
 ### 4. 架构优势
 - 纯粹的"哑视图"：View只负责UI渲染
 - 单一真理之源：ViewModel是所有状态的唯一来源
 - 可测试性：ViewModel可以独立测试
 - 可维护性：业务逻辑与UI分离
 
 ### 5. 性能优化
 - 懒加载：数据按需加载
 - 乐观更新：消息发送时立即更新UI
 - 内存管理：避免循环引用和内存泄漏
 */
