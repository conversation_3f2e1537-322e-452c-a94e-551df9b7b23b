//
//  EARepositoryContainer.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-05.
//

import Foundation
import SwiftData
import SwiftUI

/// Repository容器实现
/// 统一管理所有Repository实例，提供依赖注入支持
@MainActor
final class EARepositoryContainerImpl: EARepositoryContainer, ObservableObject {

    // MARK: - 共享Container配置（遵循开发规范）

    /// 创建共享的ModelContainer实例
    /// 遵循iOS 18+Context一致性要求，所有Repository共享同一Container
    /// ✅ 修复：使用AppSchema统一管理，确保与AppEntry.swift完全一致
    static func createSharedContainer() throws -> ModelContainer {
        return try EAAppSchema.createProductionContainer()
    }
    
    // MARK: - Repository实例
    
    private let _userRepository: EAUserRepository
    private let _userSettingsRepository: EAUserSettingsRepository
    private let _habitRepository: EAHabitRepository
    private let _completionRepository: EACompletionRepository
    private let _aiMessageRepository: EAAIMessageRepository
    private let _paymentRepository: EAPaymentRepository
    private let _analyticsRepository: EAAnalyticsRepository
    private let _contentRepository: EAContentRepository
    private let _pathRepository: EAPathRepository
    private let _challengeRepository: EAUniverseChallengeRepository


    
    // MARK: - 公开接口
    
    var userRepository: EAUserRepository { _userRepository }
    var userSettingsRepository: EAUserSettingsRepository { _userSettingsRepository }
    var habitRepository: EAHabitRepository { _habitRepository }
    var completionRepository: EACompletionRepository { _completionRepository }
    var aiMessageRepository: EAAIMessageRepository { _aiMessageRepository }
    var paymentRepository: EAPaymentRepository { _paymentRepository }
    var analyticsRepository: EAAnalyticsRepository { _analyticsRepository }
    var contentRepository: EAContentRepository { _contentRepository }
    var pathRepository: EAPathRepository { _pathRepository }
    var challengeRepository: EAUniverseChallengeRepository { _challengeRepository }


    
    // MARK: - 初始化
    
    /// 初始化Repository容器
    /// - Parameter modelContainer: SwiftData模型容器
    init(modelContainer: ModelContainer) {
        // 🔧 创建Repository容器并验证Context
        let containerContext = modelContainer.mainContext
        #if DEBUG
        // 调试环境下验证Container Context有效性
        _ = containerContext
        #endif
        
        // 创建所有Repository实例，确保使用同一个ModelContainer
        self._userRepository = EAUserRepositoryImpl(modelContainer: modelContainer)
        self._userSettingsRepository = EASwiftDataUserSettingsRepository(modelContainer: modelContainer)
        self._habitRepository = EAHabitRepositoryImpl(modelContainer: modelContainer)
        self._completionRepository = EASwiftDataCompletionRepository(modelContainer: modelContainer)
        self._aiMessageRepository = EASwiftDataAIMessageRepository(modelContainer: modelContainer)
        self._paymentRepository = EASwiftDataPaymentRepository(modelContainer: modelContainer)
        self._analyticsRepository = EASwiftDataAnalyticsRepository(modelContainer: modelContainer)
        self._contentRepository = EASwiftDataContentRepository(modelContainer: modelContainer)
        self._pathRepository = EASwiftDataPathRepository(modelContainer: modelContainer)
        self._challengeRepository = EAUniverseChallengeRepository(modelContainer: modelContainer)
        
        // ✅ 新增：验证所有Repository使用同一Container
        #if DEBUG
        validateRepositoryContainerConsistency(modelContainer)
        #endif
    }
    
    // MARK: - 性能监控器配置（阶段3新增）
    
    /// 为所有Repository配置性能监控器
    func configurePerformanceMonitoring(monitor: EARepositoryPerformanceMonitor) async {
        await _userRepository.setPerformanceMonitor(monitor)
        // 可以扩展到其他Repository
    }
    
    /// ✅ 新增：验证Repository Container一致性
    #if DEBUG
    private func validateRepositoryContainerConsistency(_ expectedContainer: ModelContainer) {
        let contextId = ObjectIdentifier(expectedContainer.mainContext)
        // 调试环境下验证Repository Container一致性
        // 确保所有Repository都使用相同的ModelContainer
        _ = contextId // 用于调试验证，确保Container ID正确生成
    }
    #endif
    
    // MARK: - 用户管理
    
    /// 获取当前用户
    /// - Returns: 当前用户实例，如果不存在则返回nil
    func getCurrentUser() async throws -> EAUser? {
        // 这里可以实现获取当前用户的逻辑
        // 例如从UserDefaults获取当前用户ID，然后通过userRepository获取用户
        return try await userRepository.fetchCurrentUser()
    }
    
    // MARK: - AI数据桥接接口（为阶段六开发准备）
    
    /// 获取用户习惯数据摘要（为AI分析准备）
    func getUserHabitSummary(userId: UUID) async throws -> EAAIHabitSummary? {
        guard await userRepository.fetchUser(by: userId) != nil else { return nil }
        
        let habits = await habitRepository.fetchUserHabits(userId: userId)
        let recentCompletions = await habitRepository.fetchRecentCompletions(userId: userId, days: 7)
        
        return EAAIHabitSummary(
            userId: userId,
            totalHabits: habits.count,
            activeHabits: habits.filter { $0.isActive }.count,
            recentCompletionRate: calculateCompletionRate(habits: habits, completions: recentCompletions),
            mostSuccessfulHabit: findMostSuccessfulHabit(habits: habits, completions: recentCompletions),
            strugglingHabits: findStrugglingHabits(habits: habits, completions: recentCompletions),
            analysisTimestamp: Date()
        )
    }
    
    /// 获取用户社区活动摘要（为AI分析准备）
    func getUserCommunityActivitySummary(userId: UUID) async throws -> EAAICommunityActivitySummary? {
        guard await userRepository.fetchUser(by: userId) != nil else { return nil }
        
        // 使用现有的fetchPosts方法获取最近的帖子
        let recentPosts = try await communityRepository.fetchPosts(limit: 50, offset: 0)
        // 过滤该用户的帖子
        let userPosts = recentPosts.filter { $0.getAuthor()?.id == userId }
        
        return EAAICommunityActivitySummary(
            userId: userId,
            totalPosts: userPosts.count,
            recentPostsCount: userPosts.filter { Calendar.current.isDate($0.creationDate, inSameDayAs: Date()) }.count,
            averageLikesPerPost: calculateAverageLikes(posts: userPosts),
            communityEngagementScore: calculateEngagementScore(posts: userPosts),
            analysisTimestamp: Date()
        )
    }
    
    // MARK: - AI数据分析辅助方法
    
    private func calculateCompletionRate(habits: [EAHabit], completions: [EACompletion]) -> Double {
        guard !habits.isEmpty else { return 0.0 }
        
        let last7Days = Calendar.current.dateInterval(of: .weekOfYear, for: Date())
        let recentCompletions = completions.filter { completion in
            guard let interval = last7Days else { return false }
            return interval.contains(completion.date)
        }
        
        let expectedCompletions = habits.filter { $0.isActive }.count * 7
        guard expectedCompletions > 0 else { return 0.0 }
        
        return Double(recentCompletions.count) / Double(expectedCompletions)
    }
    
    private func findMostSuccessfulHabit(habits: [EAHabit], completions: [EACompletion]) -> String? {
        let habitCompletionCounts = Dictionary(grouping: completions) { $0.habit?.id }
            .mapValues { $0.count }
        
        guard let (habitId, _) = habitCompletionCounts.max(by: { $0.value < $1.value }),
              let habit = habits.first(where: { $0.id == habitId }) else {
            return nil
        }
        
        return habit.name
    }
    
    private func findStrugglingHabits(habits: [EAHabit], completions: [EACompletion]) -> [String] {
        let last7Days = Calendar.current.dateInterval(of: .weekOfYear, for: Date())
        let recentCompletions = completions.filter { completion in
            guard let interval = last7Days else { return false }
            return interval.contains(completion.date)
        }
        
        let habitCompletionCounts = Dictionary(grouping: recentCompletions) { $0.habit?.id }
            .mapValues { $0.count }
        
        return habits.filter { habit in
            guard habit.isActive else { return false }
            let completionCount = habitCompletionCounts[habit.id] ?? 0
            return completionCount < 2 // 一周内完成次数少于2次视为困难
        }.map { $0.name }
    }
    
    private func calculateAverageLikes(posts: [EACommunityPost]) -> Double {
        guard !posts.isEmpty else { return 0.0 }
        let totalLikes = posts.reduce(into: 0) { $0 += $1.likeCount }
        return Double(totalLikes) / Double(posts.count)
    }
    
    private func calculateEngagementScore(posts: [EACommunityPost]) -> Double {
        let postsScore = Double(posts.count) * 10.0
        let likesScore = Double(posts.reduce(into: 0) { $0 += $1.likeCount }) * 2.0
        return postsScore + likesScore
    }
    
    /// 🔍 验证Repository Container一致性
    func verifyContainerConsistency(expectedContextId: ObjectIdentifier) {
        // 🔑 生产环境：移除调试代码，符合.cursorrules规范
        #if DEBUG
        // 调试环境下可以记录一致性验证信息，但不使用print
        #endif
        
        // 🔑 生产环境：移除调试代码，符合.cursorrules规范
        #if DEBUG
        // 调试环境下可以记录验证结果，但不使用print
        #endif
    }
    

}

// MARK: - Environment支持

/// Environment扩展，支持Repository容器依赖注入
extension EnvironmentValues {
    /// Repository容器环境值
    /// ✅ 修复：使用可选类型避免MainActor线程安全问题
    @Entry var repositoryContainer: EARepositoryContainer? = nil
}

// MARK: - 视图扩展：便捷方法

extension View {
    
    /// 注入Repository容器到环境
    /// - Parameter repositoryContainer: Repository容器实例
    /// - Returns: 配置了Repository容器的视图
    func repositoryContainer(_ repositoryContainer: EARepositoryContainer) -> some View {
        self.environment(\.repositoryContainer, repositoryContainer)
    }
}

// MARK: - 便利构造器

extension EARepositoryContainerImpl {
    
    /// 创建用于预览的Repository容器
    /// - Returns: 配置了内存存储的Repository容器
    /// ✅ 修复：使用AppSchema统一管理
    static func preview() -> EARepositoryContainerImpl {
        let container = try! EAAppSchema.createPreviewContainer()
        return EARepositoryContainerImpl(modelContainer: container)
    }
}