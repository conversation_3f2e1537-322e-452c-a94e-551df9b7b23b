# Evolve MVP 精简移除方案（最终版）

**版本：2025-07**  
**作者：AI助手与叶同学**

---

## 目录

1. [目标与原则](#目标与原则)
2. [分步操作方案](#分步操作方案)
    1. [数据模型（SwiftData）层](#1-数据模型swiftdata层)
    2. [Repository 和 Service 层](#2-repository-和-service-层)
    3. [UI 和 ViewModel 层](#3-ui-和-viewmodel-层)
    4. [AI 集成与数据桥接](#4-ai-集成与数据桥接)
    5. [全局配置与常量](#5-全局配置与常量)
    6. [SwiftData 迁移与清理](#6-swiftdata-迁移与清理)
    7. [编译与修复](#7-编译与修复)
    8. [测试与回归](#8-测试与回归)
    9. [文档更新](#9-文档更新)
3. [风险与建议](#风险与建议)
4. [结论与最短总结](#结论与最短总结)

---

## 目标与原则

**目标：**  
彻底移除「社区页面」「好友功能」「星际能量系统」，只保留核心功能：
- 自律计划发布
- 计划打卡
- 我的页面
- AI聊天页面

**操作核心原则：**
- 分层移除，先断依赖再删除
- 确保 SwiftData 关系正确
- 避免残留引用导致编译崩溃
- 每步后可编译验证，分阶段提交

---

## 分步操作方案

### 1. 数据模型（SwiftData）层

- **必须删除模型：**
  - EACommunityPost、EACommunityComment、EACommunityLike、EACommunityFollow、EACommunityReport
  - EAUniverseChallenge、EAUniverseChallengeParticipation
  - EAFriendship、EAFriendRequest、EAFriendMessage、EAFriendNotification
  - EAEnergyReward
- **主线模型调整：**
  - EAUser、EAUserSocialProfile 移除所有指向上述模型的 @Relationship 字段
  - 检查 `init` 方法，删除如 `self.friendships = []`、`self.posts = []`、`self.energyLevel = 0` 等初始化
  - 删除如 `var stellarEnergyValue: Int` 等能量相关字段
  - **用户注册/SessionManager初始化：** 检查用户注册、登录流程，确保没有为 EAUserSocialProfile 初始化多余好友/社区/能量字段
- **EAAppSchema/ModelContainer：**
  - 从所有注册中移除上述模型，只保留计划相关模型（EAHabit、EACompletion、EAUser 等）

### 2. Repository 和 Service 层

- **删除文件：**
  - EACommunityRepository、EACommunityService
  - EAFriendshipRepository、EAFriendshipService
  - EAFriendMessageRepository、EAFriendChatService
  - EAUniverseChallengeRepository
- **修改 EARepositoryContainer：**
  - 移除上述 Repository 的属性、init 注册、协议方法

### 3. UI 和 ViewModel 层

- **删除目录/文件：**
  - Features/Community/ 及所有涉及好友、社区、能量的视图和 ViewModel
  - EAFriendListView.swift、EAFriendChatView.swift、EAAddFriendView.swift、EACommunityView.swift、EAUniverseChallengeView.swift 等
- **修改主Tab（EAMainTabView.swift）：**
  - 移除“社区”“好友”“能量”Tab，确保剩余Tabs索引正确
  - 移除相关导航链接和Sheet调用

### 4. AI 集成与数据桥接

- **删除或精简：**
  - EACommunityAIDataBridge、EAAIFriendChatContext
  - AI提示模板里涉及好友/社区的段落
- **检查剩余：**
  - 确保 EAAIService 只处理计划养成、习惯打卡数据
  - 没有跨模型调用到被删除的Repository

### 5. 全局配置与常量

- **清理文件：**
  - AppConstants.swift：删除所有社区、好友、能量相关常量
  - EAFeatureManager.swift：移除社区、好友、能量的开关逻辑

### 6. SwiftData 迁移与清理

- **必须操作：**
  - 删除/卸载 App，清理本地 SwiftData 存储文件
  - iOS模拟器：长按App图标 → 删除App
  - 真机：卸载 → 重新安装
  - 避免Schema冲突崩溃

### 7. 编译与修复

- 全面编译，修复所有引用错误
- 重点检查：Tab入口、NavigationLink、Sheet调用、Repository依赖注入、AI调用是否干净

### 8. 测试与回归

- **功能测试：** 计划发布、计划打卡、AI聊天、我的页面
- **兼容性测试：** 不同屏幕尺寸
- **崩溃监控：** 重点关注启动和主流程

### 9. 文档更新

- 更新 README.md，列出移除模块和MVP保留范围
- 更新 技术架构文档.md，调整后的模型和模块、新的容器依赖注入结构

---

## 风险与建议

- **最大风险点：**
  - SwiftData inverse关系未清
  - Tab入口引用崩溃
  - AI桥接残留调用
- **建议：**
  - 每大步移除 → 编译 → 提交
  - 全项目完整备份
  - 用户注册/SessionManager初始化要同步检查，避免残留好友/社区/能量相关初始化代码
  - 每次大幅度模型调整后，务必卸载App再运行

---

## 结论与最短总结

这套分层、分步、全局梳理的方案能最大程度保证主线功能安全。执行过程中如遇到任何不确定，先暂停 → 备份 → 确认 → 再继续。

**最短总结版：**
1. 先删模型、断关系
2. 再删Repository/Service
3. 再删UI/ViewModel
4. 再清理AI桥接
5. 再删全局配置
6. 最后卸载App → 彻底清理本地存储
7. 编译修复
8. 回归测试

---

**版本号：2025-07**  
**作者：AI助手与叶同学**